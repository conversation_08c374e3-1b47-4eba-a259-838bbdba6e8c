#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gaziantep Bitki Öneri Sistemi Test Dosyası
Bu dosya modelin doğru çalışıp çalışmadığını test eder.
"""

from plant_prediction_model import PlantPredictionModel
from datetime import datetime
import json

def test_model():
    """Model testlerini çalıştır"""
    print("🧪 GAZİANTEP BİTKİ ÖNERİ SİSTEMİ TEST BAŞLATIYOR...")
    print("=" * 60)
    
    # Model oluştur
    model = PlantPredictionModel()
    
    # Test senaryoları
    test_scenarios = [
        {
            "name": "Yaz Sıcak Gün",
            "temperature": 32,
            "humidity": 45,
            "season": "yaz"
        },
        {
            "name": "İlkbahar Ideal",
            "temperature": 22,
            "humidity": 65,
            "season": "ilkbahar"
        },
        {
            "name": "Kış Soğuk",
            "temperature": 8,
            "humidity": 75,
            "season": "kış"
        },
        {
            "name": "Sonbahar Nemli",
            "temperature": 18,
            "humidity": 80,
            "season": "sonbahar"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 TEST SENARYOSU: {scenario['name']}")
        print(f"🌡️ Sıcaklık: {scenario['temperature']}°C")
        print(f"💧 Nem: {scenario['humidity']}%")
        print(f"🗓️ Mevsim: {scenario['season']}")
        print("-" * 40)
        
        # Tüm bitkiler için önerileri al
        recommendations = model.get_all_recommendations(
            scenario['temperature'], 
            scenario['humidity']
        )
        
        # En iyi 3 öneriyi göster
        print("🏆 EN İYİ 3 ÖNERİ:")
        for i, rec in enumerate(recommendations[:3], 1):
            emoji = "🌟" if rec['score'] >= 80 else "👍" if rec['score'] >= 60 else "⚠️" if rec['score'] >= 40 else "❌"
            print(f"  {i}. {emoji} {rec['plant_name'].upper()}: {rec['score']:.1f}/100")
            print(f"     {rec['advice']}")
        
        print()

def test_specific_plants():
    """Belirli bitkiler için detaylı test"""
    print("\n🔍 BELİRLİ BİTKİLER İÇİN DETAYLI TEST")
    print("=" * 60)
    
    model = PlantPredictionModel()
    
    # Test bitkileri
    test_plants = ['domates', 'biber', 'marul', 'pothos', 'sansevieria']
    
    # Gaziantep yaz koşulları
    temperature = 28
    humidity = 55
    
    print(f"🌡️ Test Koşulları: {temperature}°C, {humidity}% nem")
    print("-" * 40)
    
    for plant in test_plants:
        rec = model.get_recommendation(temperature, humidity, plant)
        plant_info = model.plant_database[plant]
        
        print(f"\n🌱 {plant.upper()}")
        print(f"   Skor: {rec['score']:.1f}/100 ({rec['status']})")
        print(f"   Optimum: {plant_info['temp_optimal']}°C, {plant_info['humidity_optimal']}% nem")
        print(f"   Öneri: {rec['advice']}")
        
        if rec['suggestions']:
            print(f"   Öneriler: {', '.join(rec['suggestions'])}")

def test_seasonal_variations():
    """Mevsimsel değişimleri test et"""
    print("\n📅 MEVSİMSEL DEĞİŞİM TESTİ")
    print("=" * 60)
    
    model = PlantPredictionModel()
    
    # Gaziantep'in mevsimsel ortalama değerleri
    seasonal_data = {
        'ilkbahar': {'temp': 20, 'humidity': 65},
        'yaz': {'temp': 30, 'humidity': 45},
        'sonbahar': {'temp': 18, 'humidity': 70},
        'kis': {'temp': 8, 'humidity': 75}
    }
    
    test_plant = 'domates'
    
    print(f"🍅 {test_plant.upper()} için mevsimsel analiz:")
    print("-" * 40)
    
    for season, conditions in seasonal_data.items():
        rec = model.get_recommendation(
            conditions['temp'], 
            conditions['humidity'], 
            test_plant
        )
        
        emoji = "🌟" if rec['score'] >= 80 else "👍" if rec['score'] >= 60 else "⚠️" if rec['score'] >= 40 else "❌"
        
        print(f"{season.upper():>10}: {emoji} {rec['score']:.1f}/100 - {rec['status']}")

def test_api_simulation():
    """API benzeri kullanım testi"""
    print("\n🔌 API SİMÜLASYON TESTİ")
    print("=" * 60)
    
    model = PlantPredictionModel()
    
    # Simüle edilmiş API istekleri
    api_requests = [
        {
            "endpoint": "/predict",
            "data": {"temperature": 25, "humidity": 60}
        },
        {
            "endpoint": "/predict",
            "data": {"temperature": 25, "humidity": 60, "plant_name": "domates"}
        },
        {
            "endpoint": "/recommend/custom",
            "data": {"temperature": 22, "humidity": 70, "plant_type": "sebze"}
        }
    ]
    
    for i, request in enumerate(api_requests, 1):
        print(f"\n📡 API İsteği {i}: {request['endpoint']}")
        print(f"📝 Veri: {json.dumps(request['data'], ensure_ascii=False)}")
        
        try:
            if request['data'].get('plant_name'):
                # Belirli bitki için tahmin
                result = model.get_recommendation(
                    request['data']['temperature'],
                    request['data']['humidity'],
                    request['data']['plant_name']
                )
                print(f"✅ Sonuç: {result['plant_name']} - {result['score']:.1f}/100")
            else:
                # Tüm bitkiler için tahmin
                results = model.get_all_recommendations(
                    request['data']['temperature'],
                    request['data']['humidity']
                )
                
                # Tip filtresi varsa uygula
                if request['data'].get('plant_type') and request['data']['plant_type'] != 'all':
                    filtered_results = []
                    for result in results:
                        plant_info = model.plant_database.get(result['plant_name'], {})
                        if plant_info.get('type') == request['data']['plant_type']:
                            filtered_results.append(result)
                    results = filtered_results
                
                print(f"✅ Sonuç: {len(results)} bitki önerisi")
                if results:
                    best = results[0]
                    print(f"   En iyi: {best['plant_name']} - {best['score']:.1f}/100")
                    
        except Exception as e:
            print(f"❌ Hata: {str(e)}")

def test_edge_cases():
    """Uç durumları test et"""
    print("\n⚠️  UÇ DURUMLAR TESTİ")
    print("=" * 60)
    
    model = PlantPredictionModel()
    
    edge_cases = [
        {"name": "Çok Sıcak", "temp": 45, "humidity": 30},
        {"name": "Çok Soğuk", "temp": -5, "humidity": 90},
        {"name": "Çok Nemli", "temp": 25, "humidity": 95},
        {"name": "Çok Kuru", "temp": 25, "humidity": 10},
    ]
    
    for case in edge_cases:
        print(f"\n🔥 {case['name']}: {case['temp']}°C, {case['humidity']}%")
        
        try:
            recommendations = model.get_all_recommendations(case['temp'], case['humidity'])
            
            if recommendations:
                best = recommendations[0]
                worst = recommendations[-1]
                
                print(f"   En iyi: {best['plant_name']} ({best['score']:.1f}/100)")
                print(f"   En kötü: {worst['plant_name']} ({worst['score']:.1f}/100)")
                
                # Hiç uygun bitki var mı?
                suitable_count = len([r for r in recommendations if r['score'] >= 60])
                print(f"   Uygun bitki sayısı (≥60): {suitable_count}/{len(recommendations)}")
            else:
                print("   ❌ Hiç öneri bulunamadı")
                
        except Exception as e:
            print(f"   ❌ Hata: {str(e)}")

def main():
    """Ana test fonksiyonu"""
    try:
        print("🌱 GAZİANTEP BİTKİ ÖNERİ SİSTEMİ")
        print("🧪 KAPSAMLI TEST SÜİTİ")
        print("=" * 60)
        
        # Tüm testleri çalıştır
        test_model()
        test_specific_plants()
        test_seasonal_variations()
        test_api_simulation()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("✅ TÜM TESTLER TAMAMLANDI!")
        print("🎉 Model başarıyla çalışıyor!")
        
    except Exception as e:
        print(f"\n❌ TEST HATASI: {str(e)}")
        print("🔧 Lütfen model kodunu kontrol edin.")

if __name__ == "__main__":
    main()
