version: '3.8'

services:
  plant-api:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY}
      - DEFAULT_LATITUDE=37.0662
      - DEFAULT_LONGITUDE=37.3833
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy (opsiyonel)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - plant-api
    restart: unless-stopped

volumes:
  logs:
