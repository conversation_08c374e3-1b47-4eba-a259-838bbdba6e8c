# 🌱 Gaziantep Bitki Öneri Sistemi

Gaziantep için sebze ve ev bitkileri yetiştirme önerilerini makine öğrenmesi ile sunan sistem.

## 🚀 Özellikler

- **Gerçek Zamanlı Hava Durumu**: OpenWeatherMap API entegrasyonu
- **Ak<PERSON><PERSON><PERSON>**: Sıcaklık, nem ve mevsim verilerine dayalı ML modeli
- **Konum Bazlı Öneriler**: GPS koordinatlarına göre özelleştirilmiş öneriler
- **Çoklu Bitki Desteği**: Sebzeler ve ev bitkileri için ayrı kategoriler
- **React Native Entegrasyonu**: Mobil uygulama için hazır API client

## 📱 Desteklenen Bitkiler

### Sebzeler
- 🍅 Domates
- 🌶️ Biber  
- 🍆 Patlıcan
- 🥒 Salatalık
- 🥬 Marul
- 🥬 Ispanak

### Ev Bitkileri
- 🪴 Pothos
- 🌿 Monstera
- 🌵 Sansevieria
- 🌳 Ficus

## 🛠️ Kurulum

### Python Backend

1. **Sanal ortam oluştur:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# veya
venv\Scripts\activate     # Windows
```

2. **Bağımlılıkları yükle:**
```bash
pip install -r requirements.txt
```

3. **OpenWeatherMap API Key ayarla:**
- [OpenWeatherMap](https://openweathermap.org/api) hesabı oluştur
- API key'ini al
- `plant_prediction_model.py` dosyasında `YOUR_API_KEY` yerine gerçek key'ini yaz

4. **Sunucuyu başlat:**
```bash
python app.py
```

API şu adreste çalışacak: `http://localhost:5000`

### React Native Entegrasyonu

1. **API Client'ı projenize ekleyin:**
```bash
# AsyncStorage bağımlılığını yükleyin
npm install @react-native-async-storage/async-storage
```

2. **PlantPredictionAPI.js dosyasını projenize kopyalayın**

3. **Kullanım örneği:**
```javascript
import plantAPI from './PlantPredictionAPI';

// Konum bazlı öneriler al
const recommendations = await plantAPI.getLocationBasedRecommendations(37.0662, 37.3833);

// Özel koşullar için öneri
const customRec = await plantAPI.getCustomRecommendations(25, 65, 'sebze');
```

## 📊 API Endpoints

### GET `/health`
API sağlık kontrolü

### GET `/weather/{lat}/{lon}`
Koordinatlara göre hava durumu

### POST `/predict`
Bitki uygunluk tahmini
```json
{
  "temperature": 25.0,
  "humidity": 65.0,
  "plant_name": "domates" // opsiyonel
}
```

### GET `/plants`
Mevcut bitki listesi

### POST `/recommend/location`
Konum bazlı öneri
```json
{
  "latitude": 37.0662,
  "longitude": 37.3833
}
```

### POST `/recommend/custom`
Özel koşullar için öneri
```json
{
  "temperature": 25.0,
  "humidity": 65.0,
  "plant_type": "sebze" // "sebze", "ev_bitkisi", "all"
}
```

### GET `/plant/{plant_name}/details`
Bitki detayları

## 🧠 Model Açıklaması

### Özellikler (Features)
- Sıcaklık ve nem değerleri
- Mevsimsel veriler
- Bitki özel gereksinimleri
- Optimum koşullardan sapma

### Skorlama Sistemi
- **80-100**: Mükemmel koşullar 🌟
- **60-79**: İyi koşullar 👍
- **40-59**: Orta koşullar ⚠️
- **0-39**: Zor koşullar ❌

### Hesaplama Yöntemi
```
Toplam Skor = (Sıcaklık Skoru × 0.4) + (Nem Skoru × 0.4) + (Mevsim Skoru × 0.2)
```

## 🌍 Gaziantep Özel Ayarları

- **Koordinatlar**: 37.0662°N, 37.3833°E
- **İklim**: Karasal iklim özellikleri
- **Mevsimsel Adaptasyon**: Yerel mevsim koşulları

## 🔧 Geliştirme

### Test Çalıştırma
```bash
python plant_prediction_model.py
```

### Yeni Bitki Ekleme
`plant_prediction_model.py` dosyasındaki `_create_plant_database` metoduna yeni bitki ekleyin:

```python
'yeni_bitki': {
    'temp_min': 15, 'temp_max': 30, 'temp_optimal': 22,
    'humidity_min': 50, 'humidity_max': 80, 'humidity_optimal': 65,
    'season': ['ilkbahar', 'yaz'], 'type': 'sebze',
    'growth_days': 60, 'water_need': 'orta'
}
```

## 📈 Gelecek Geliştirmeler

- [ ] Toprak analizi entegrasyonu
- [ ] Hastalık ve zararlı tahmini
- [ ] Sulama programı önerileri
- [ ] Hasat zamanı tahmini
- [ ] Verim tahmin modeli
- [ ] Çoklu dil desteği
- [ ] Offline mod

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Commit yapın (`git commit -am 'Yeni özellik eklendi'`)
4. Push yapın (`git push origin feature/yeni-ozellik`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 İletişim

Sorularınız için issue açabilir veya pull request gönderebilirsiniz.

---

**Not**: Bu sistem Gaziantep iklim koşulları için optimize edilmiştir. Farklı bölgeler için model parametrelerinin ayarlanması gerekebilir.
