#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API Test Dosyası - Flask API'nin çalışıp çalışmadığını test eder
"""

import requests
import json

def test_api():
    base_url = "http://localhost:5000"
    
    print("🧪 API TEST BAŞLATIYOR...")
    print("=" * 50)
    
    # 1. Health Check
    print("\n1️⃣ Health Check Testi")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Hata: {e}")
    
    # 2. Bitki Listesi
    print("\n2️⃣ Bitki Listesi Testi")
    try:
        response = requests.get(f"{base_url}/plants")
        data = response.json()
        print(f"Status: {response.status_code}")
        print(f"Bitki sayısı: {data['count']}")
        print(f"İlk bitki: {data['data'][0]['name']}")
    except Exception as e:
        print(f"❌ Hata: {e}")
    
    # 3. <PERSON><PERSON><PERSON>
    print("\n3️⃣ Tahmin Testi")
    try:
        test_data = {
            "temperature": 25.0,
            "humidity": 65.0
        }
        response = requests.post(f"{base_url}/predict", json=test_data)
        data = response.json()
        print(f"Status: {response.status_code}")
        print(f"Öneri sayısı: {len(data['data'])}")
        print(f"En iyi öneri: {data['data'][0]['plant_name']} - {data['data'][0]['score']}/100")
    except Exception as e:
        print(f"❌ Hata: {e}")
    
    # 4. Belirli Bitki Testi
    print("\n4️⃣ Belirli Bitki Testi")
    try:
        test_data = {
            "temperature": 25.0,
            "humidity": 65.0,
            "plant_name": "domates"
        }
        response = requests.post(f"{base_url}/predict", json=test_data)
        data = response.json()
        print(f"Status: {response.status_code}")
        print(f"Domates skoru: {data['data']['score']}/100")
        print(f"Öneri: {data['data']['advice']}")
    except Exception as e:
        print(f"❌ Hata: {e}")
    
    # 5. Konum Bazlı Öneri
    print("\n5️⃣ Konum Bazlı Öneri Testi")
    try:
        test_data = {
            "latitude": 37.0662,
            "longitude": 37.3833
        }
        response = requests.post(f"{base_url}/recommend/location", json=test_data)
        data = response.json()
        print(f"Status: {response.status_code}")
        print(f"Hava durumu: {data['data']['weather']['temperature']}°C, {data['data']['weather']['humidity']}%")
        print(f"En iyi öneri: {data['data']['best_plants'][0]['plant_name']} - {data['data']['best_plants'][0]['score']}/100")
    except Exception as e:
        print(f"❌ Hata: {e}")
    
    print("\n" + "=" * 50)
    print("✅ API TESTLERI TAMAMLANDI!")

if __name__ == "__main__":
    test_api()
