// React Native için Plant Prediction API Client
import AsyncStorage from '@react-native-async-storage/async-storage';

class PlantPredictionAPI {
  constructor(baseURL = 'http://localhost:5000') {
    this.baseURL = baseURL;
    this.timeout = 10000; // 10 saniye timeout
  }

  // API isteği için yardımcı method
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Sağlık kontrolü
  async healthCheck() {
    try {
      const response = await this.makeRequest('/health');
      return response;
    } catch (error) {
      throw new Error('API bağlantısı kurulamadı');
    }
  }

  // Hava durumu verilerini al
  async getWeather(latitude, longitude) {
    try {
      const response = await this.makeRequest(`/weather/${latitude}/${longitude}`);
      
      if (response.success) {
        // Cache'e kaydet
        await AsyncStorage.setItem('lastWeatherData', JSON.stringify({
          data: response.data,
          timestamp: new Date().toISOString(),
          location: { latitude, longitude }
        }));
        
        return response.data;
      } else {
        throw new Error(response.error || 'Hava durumu verisi alınamadı');
      }
    } catch (error) {
      // Cache'den son veriyi dene
      try {
        const cachedData = await AsyncStorage.getItem('lastWeatherData');
        if (cachedData) {
          const parsed = JSON.parse(cachedData);
          const cacheAge = new Date() - new Date(parsed.timestamp);
          
          // 1 saat içindeki cache'i kullan
          if (cacheAge < 3600000) {
            console.log('Cache\'den hava durumu verisi kullanılıyor');
            return parsed.data;
          }
        }
      } catch (cacheError) {
        console.error('Cache okuma hatası:', cacheError);
      }
      
      throw error;
    }
  }

  // Bitki uygunluk tahmini
  async predictPlantSuitability(temperature, humidity, plantName = null, date = null) {
    try {
      const requestData = {
        temperature,
        humidity,
      };

      if (plantName) {
        requestData.plant_name = plantName;
      }

      if (date) {
        requestData.date = date;
      }

      const response = await this.makeRequest('/predict', {
        method: 'POST',
        body: JSON.stringify(requestData),
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Tahmin yapılamadı');
      }
    } catch (error) {
      throw error;
    }
  }

  // Mevcut bitki listesini al
  async getAvailablePlants() {
    try {
      const response = await this.makeRequest('/plants');
      
      if (response.success) {
        // Cache'e kaydet
        await AsyncStorage.setItem('plantsList', JSON.stringify(response.data));
        return response.data;
      } else {
        throw new Error(response.error || 'Bitki listesi alınamadı');
      }
    } catch (error) {
      // Cache'den dene
      try {
        const cachedPlants = await AsyncStorage.getItem('plantsList');
        if (cachedPlants) {
          console.log('Cache\'den bitki listesi kullanılıyor');
          return JSON.parse(cachedPlants);
        }
      } catch (cacheError) {
        console.error('Cache okuma hatası:', cacheError);
      }
      
      throw error;
    }
  }

  // Konum bazlı öneri
  async getLocationBasedRecommendations(latitude, longitude) {
    try {
      const response = await this.makeRequest('/recommend/location', {
        method: 'POST',
        body: JSON.stringify({
          latitude,
          longitude,
        }),
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Konum bazlı öneri alınamadı');
      }
    } catch (error) {
      throw error;
    }
  }

  // Özel koşullar için öneri
  async getCustomRecommendations(temperature, humidity, plantType = 'all') {
    try {
      const response = await this.makeRequest('/recommend/custom', {
        method: 'POST',
        body: JSON.stringify({
          temperature,
          humidity,
          plant_type: plantType,
        }),
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Özel öneri alınamadı');
      }
    } catch (error) {
      throw error;
    }
  }

  // Bitki detaylarını al
  async getPlantDetails(plantName, temperature = null, humidity = null) {
    try {
      let endpoint = `/plant/${plantName}/details`;
      
      if (temperature !== null && humidity !== null) {
        endpoint += `?temp=${temperature}&humidity=${humidity}`;
      }

      const response = await this.makeRequest(endpoint);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Bitki detayları alınamadı');
      }
    } catch (error) {
      throw error;
    }
  }

  // Konum izni ve hava durumu alma
  async getCurrentLocationWeather() {
    try {
      // React Native Geolocation kullanımı
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      });

      const { latitude, longitude } = position.coords;
      const weatherData = await this.getWeather(latitude, longitude);

      return {
        location: { latitude, longitude },
        weather: weatherData,
      };
    } catch (error) {
      // Gaziantep default koordinatları
      console.log('Konum alınamadı, Gaziantep koordinatları kullanılıyor');
      const defaultLat = 37.0662;
      const defaultLon = 37.3833;
      
      const weatherData = await this.getWeather(defaultLat, defaultLon);
      
      return {
        location: { latitude: defaultLat, longitude: defaultLon },
        weather: weatherData,
      };
    }
  }

  // Öneri skoruna göre renk döndür
  getScoreColor(score) {
    if (score >= 80) return '#4CAF50'; // Yeşil
    if (score >= 60) return '#8BC34A'; // Açık yeşil
    if (score >= 40) return '#FF9800'; // Turuncu
    return '#F44336'; // Kırmızı
  }

  // Öneri skoruna göre emoji döndür
  getScoreEmoji(score) {
    if (score >= 80) return '🌟';
    if (score >= 60) return '👍';
    if (score >= 40) return '⚠️';
    return '❌';
  }

  // Bitki tipine göre emoji döndür
  getPlantTypeEmoji(plantType) {
    switch (plantType) {
      case 'sebze':
        return '🥬';
      case 'ev_bitkisi':
        return '🪴';
      default:
        return '🌱';
    }
  }
}

// Singleton instance
const plantAPI = new PlantPredictionAPI();

export default plantAPI;

// React Native component örneği kullanımı:
/*
import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, ActivityIndicator, Alert } from 'react-native';
import plantAPI from './PlantPredictionAPI';

export const PlantRecommendationScreen = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [weather, setWeather] = useState(null);

  useEffect(() => {
    loadRecommendations();
  }, []);

  const loadRecommendations = async () => {
    try {
      setLoading(true);

      // Mevcut konum ve hava durumunu al
      const locationWeather = await plantAPI.getCurrentLocationWeather();
      setWeather(locationWeather.weather);

      // Konum bazlı önerileri al
      const recommendations = await plantAPI.getLocationBasedRecommendations(
        locationWeather.location.latitude,
        locationWeather.location.longitude
      );

      setRecommendations(recommendations.recommendations);
    } catch (error) {
      console.error('Öneriler yüklenirken hata:', error);
      Alert.alert('Hata', 'Öneriler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bitki Önerileri</Text>

      {weather && (
        <View style={styles.weatherCard}>
          <Text>🌡️ Sıcaklık: {weather.temperature}°C</Text>
          <Text>💧 Nem: {weather.humidity}%</Text>
        </View>
      )}

      {loading ? (
        <ActivityIndicator size="large" color="#4CAF50" />
      ) : (
        <FlatList
          data={recommendations}
          keyExtractor={(item) => item.plant_name}
          renderItem={({ item }) => (
            <View style={[styles.plantCard, { borderLeftColor: item.color }]}>
              <Text style={styles.plantName}>
                {plantAPI.getPlantTypeEmoji(item.type)} {item.plant_name.toUpperCase()}
              </Text>
              <Text style={styles.score}>
                {plantAPI.getScoreEmoji(item.score)} Skor: {item.score}/100
              </Text>
              <Text style={styles.advice}>{item.advice}</Text>
            </View>
          )}
        />
      )}
    </View>
  );
};
*/
