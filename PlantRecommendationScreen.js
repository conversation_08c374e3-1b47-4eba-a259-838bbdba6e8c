import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Alert,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
} from 'react-native';
import plantAPI from './PlantPredictionAPI';

const PlantRecommendationScreen = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [weather, setWeather] = useState(null);
  const [location, setLocation] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedType, setSelectedType] = useState('all');

  useEffect(() => {
    loadRecommendations();
  }, [selectedType]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      
      // Mevcut konum ve hava durumunu al
      const locationWeather = await plantAPI.getCurrentLocationWeather();
      setWeather(locationWeather.weather);
      setLocation(locationWeather.location);
      
      // Seçilen tipe göre önerileri al
      let recommendationsData;
      if (selectedType === 'all') {
        const result = await plantAPI.getLocationBasedRecommendations(
          locationWeather.location.latitude,
          locationWeather.location.longitude
        );
        recommendationsData = result.recommendations;
      } else {
        const result = await plantAPI.getCustomRecommendations(
          locationWeather.weather.temperature,
          locationWeather.weather.humidity,
          selectedType
        );
        recommendationsData = result.recommendations;
      }
      
      setRecommendations(recommendationsData);
    } catch (error) {
      console.error('Öneriler yüklenirken hata:', error);
      Alert.alert('Hata', 'Öneriler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRecommendations();
    setRefreshing(false);
  };

  const renderPlantCard = ({ item }) => (
    <TouchableOpacity 
      style={[styles.plantCard, { borderLeftColor: plantAPI.getScoreColor(item.score) }]}
      onPress={() => showPlantDetails(item)}
    >
      <View style={styles.plantHeader}>
        <Text style={styles.plantName}>
          {plantAPI.getPlantTypeEmoji(getPlantType(item.plant_name))} {item.plant_name.toUpperCase()}
        </Text>
        <View style={[styles.scoreContainer, { backgroundColor: plantAPI.getScoreColor(item.score) }]}>
          <Text style={styles.scoreText}>{Math.round(item.score)}</Text>
        </View>
      </View>
      
      <Text style={styles.status}>
        {plantAPI.getScoreEmoji(item.score)} {item.status}
      </Text>
      
      <Text style={styles.advice}>{item.advice}</Text>
      
      {item.suggestions && item.suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <Text style={styles.suggestionsTitle}>💡 Öneriler:</Text>
          {item.suggestions.map((suggestion, index) => (
            <Text key={index} style={styles.suggestion}>• {suggestion}</Text>
          ))}
        </View>
      )}
      
      <View style={styles.conditionsContainer}>
        <Text style={styles.conditionText}>
          🌡️ Optimum: {item.optimal_temp}°C | 💧 Optimum: {item.optimal_humidity}%
        </Text>
      </View>
    </TouchableOpacity>
  );

  const getPlantType = (plantName) => {
    const vegetables = ['domates', 'biber', 'patlican', 'salatalik', 'marul', 'ispanak'];
    return vegetables.includes(plantName) ? 'sebze' : 'ev_bitkisi';
  };

  const showPlantDetails = (plant) => {
    Alert.alert(
      `${plant.plant_name.toUpperCase()} Detayları`,
      `Skor: ${Math.round(plant.score)}/100\n\n${plant.advice}\n\nMevcut Koşullar:\n🌡️ ${plant.current_temp}°C\n💧 ${plant.current_humidity}%\n\nOptimum Koşullar:\n🌡️ ${plant.optimal_temp}°C\n💧 ${plant.optimal_humidity}%`,
      [{ text: 'Tamam' }]
    );
  };

  const renderTypeFilter = () => (
    <View style={styles.filterContainer}>
      <TouchableOpacity
        style={[styles.filterButton, selectedType === 'all' && styles.filterButtonActive]}
        onPress={() => setSelectedType('all')}
      >
        <Text style={[styles.filterText, selectedType === 'all' && styles.filterTextActive]}>
          🌱 Tümü
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.filterButton, selectedType === 'sebze' && styles.filterButtonActive]}
        onPress={() => setSelectedType('sebze')}
      >
        <Text style={[styles.filterText, selectedType === 'sebze' && styles.filterTextActive]}>
          🥬 Sebzeler
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.filterButton, selectedType === 'ev_bitkisi' && styles.filterButtonActive]}
        onPress={() => setSelectedType('ev_bitkisi')}
      >
        <Text style={[styles.filterText, selectedType === 'ev_bitkisi' && styles.filterTextActive]}>
          🪴 Ev Bitkileri
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🌱 Gaziantep Bitki Önerileri</Text>
      
      {weather && location && (
        <View style={styles.weatherCard}>
          <Text style={styles.weatherTitle}>📍 Mevcut Koşullar</Text>
          <View style={styles.weatherInfo}>
            <Text style={styles.weatherText}>🌡️ {weather.temperature}°C</Text>
            <Text style={styles.weatherText}>💧 {weather.humidity}%</Text>
            <Text style={styles.weatherText}>🌤️ {weather.description}</Text>
          </View>
          <Text style={styles.locationText}>
            📍 {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
          </Text>
        </View>
      )}

      {renderTypeFilter()}

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Öneriler yükleniyor...</Text>
        </View>
      ) : (
        <FlatList
          data={recommendations}
          keyExtractor={(item) => item.plant_name}
          renderItem={renderPlantCard}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Henüz öneri bulunamadı</Text>
              <TouchableOpacity style={styles.retryButton} onPress={loadRecommendations}>
                <Text style={styles.retryText}>🔄 Tekrar Dene</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingTop: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#2E7D32',
  },
  weatherCard: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  weatherTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  weatherInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  weatherText: {
    fontSize: 14,
    color: '#666',
  },
  locationText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  filterTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  plantCard: {
    backgroundColor: 'white',
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  plantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  scoreContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  status: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#555',
  },
  advice: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  suggestionsContainer: {
    backgroundColor: '#f9f9f9',
    padding: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  suggestionsTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  suggestion: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  conditionsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 8,
  },
  conditionText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default PlantRecommendationScreen;
