from flask import Flask, request, jsonify
from flask_cors import CORS
from plant_prediction_model import PlantPredictionModel
import requests
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)  # React Native için CORS ayarı

# Model instance'ı oluştur
prediction_model = PlantPredictionModel()

@app.route('/health', methods=['GET'])
def health_check():
    """API sağlık kontrolü"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Plant Prediction API'
    })

@app.route('/weather/<float:lat>/<float:lon>', methods=['GET'])
def get_weather(lat, lon):
    """Koordinatlara göre hava durumu bilgisi al"""
    try:
        weather_data = prediction_model.get_weather_data(lat, lon)
        return jsonify({
            'success': True,
            'data': weather_data,
            'location': {
                'latitude': lat,
                'longitude': lon
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/predict', methods=['POST'])
def predict_plant_suitability():
    """Bitki uygunluk tahmini yap"""
    try:
        data = request.get_json()
        
        # Gerekli parametreleri kontrol et
        required_fields = ['temperature', 'humidity']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        temperature = float(data['temperature'])
        humidity = float(data['humidity'])
        plant_name = data.get('plant_name', None)
        
        # Tarih parametresi (opsiyonel)
        date_str = data.get('date', None)
        date = datetime.fromisoformat(date_str) if date_str else None
        
        if plant_name:
            # Belirli bir bitki için tahmin
            recommendation = prediction_model.get_recommendation(
                temperature, humidity, plant_name, date
            )
            return jsonify({
                'success': True,
                'data': recommendation
            })
        else:
            # Tüm bitkiler için tahmin
            recommendations = prediction_model.get_all_recommendations(
                temperature, humidity, date
            )
            return jsonify({
                'success': True,
                'data': recommendations
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'Invalid data format: {str(e)}'
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/plants', methods=['GET'])
def get_available_plants():
    """Mevcut bitki listesini döndür"""
    try:
        plants = list(prediction_model.plant_database.keys())
        plant_details = []
        
        for plant_name in plants:
            plant_info = prediction_model.plant_database[plant_name]
            plant_details.append({
                'name': plant_name,
                'type': plant_info['type'],
                'optimal_temp': plant_info['temp_optimal'],
                'optimal_humidity': plant_info['humidity_optimal'],
                'seasons': plant_info['season'],
                'growth_days': plant_info['growth_days'],
                'water_need': plant_info['water_need']
            })
        
        return jsonify({
            'success': True,
            'data': plant_details,
            'count': len(plant_details)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/recommend/location', methods=['POST'])
def recommend_by_location():
    """Konum bazlı öneri sistemi"""
    try:
        data = request.get_json()
        
        # Koordinatları al
        lat = float(data.get('latitude', 37.0662))  # Gaziantep default
        lon = float(data.get('longitude', 37.3833))
        
        # Hava durumu verilerini çek
        weather_data = prediction_model.get_weather_data(lat, lon)
        
        # Tüm bitkiler için önerileri al
        recommendations = prediction_model.get_all_recommendations(
            weather_data['temperature'],
            weather_data['humidity']
        )
        
        # Sonucu formatla
        result = {
            'location': {
                'latitude': lat,
                'longitude': lon
            },
            'weather': weather_data,
            'recommendations': recommendations,
            'best_plants': recommendations[:5],  # En iyi 5 öneri
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/recommend/custom', methods=['POST'])
def recommend_custom_conditions():
    """Özel koşullar için öneri"""
    try:
        data = request.get_json()
        
        temperature = float(data['temperature'])
        humidity = float(data['humidity'])
        plant_type = data.get('plant_type', 'all')  # 'sebze', 'ev_bitkisi', 'all'
        
        # Tüm önerileri al
        all_recommendations = prediction_model.get_all_recommendations(
            temperature, humidity
        )
        
        # Bitki tipine göre filtrele
        if plant_type != 'all':
            filtered_recommendations = []
            for rec in all_recommendations:
                plant_info = prediction_model.plant_database.get(rec['plant_name'], {})
                if plant_info.get('type') == plant_type:
                    filtered_recommendations.append(rec)
            recommendations = filtered_recommendations
        else:
            recommendations = all_recommendations
        
        return jsonify({
            'success': True,
            'data': {
                'conditions': {
                    'temperature': temperature,
                    'humidity': humidity,
                    'plant_type': plant_type
                },
                'recommendations': recommendations,
                'count': len(recommendations)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/plant/<plant_name>/details', methods=['GET'])
def get_plant_details(plant_name):
    """Belirli bir bitkinin detaylarını al"""
    try:
        plant_info = prediction_model.plant_database.get(plant_name.lower())
        
        if not plant_info:
            return jsonify({
                'success': False,
                'error': f'Plant "{plant_name}" not found'
            }), 404
        
        # Mevcut hava koşulları için öneri ekle
        temperature = request.args.get('temp', type=float)
        humidity = request.args.get('humidity', type=float)
        
        result = {
            'name': plant_name,
            'details': plant_info
        }
        
        if temperature is not None and humidity is not None:
            recommendation = prediction_model.get_recommendation(
                temperature, humidity, plant_name
            )
            result['current_recommendation'] = recommendation
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    print("🌱 Plant Prediction API Starting...")
    print(f"📍 Port: {port}")
    print(f"🔧 Debug: {debug}")
    print("🚀 Ready to serve plant recommendations!")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
