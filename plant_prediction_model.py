import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import joblib
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class PlantPredictionModel:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.plant_database = self._create_plant_database()
        
    def _create_plant_database(self):
        """Sebze ve ev bitkileri için optimum koşullar veritabanı"""
        plants = {
            # Sebzeler
            'domates': {
                'temp_min': 18, 'temp_max': 30, 'temp_optimal': 24,
                'humidity_min': 60, 'humidity_max': 80, 'humidity_optimal': 70,
                'season': ['ilkbahar', 'yaz'], 'type': 'sebze',
                'growth_days': 80, 'water_need': 'orta'
            },
            'biber': {
                'temp_min': 20, 'temp_max': 32, 'temp_optimal': 26,
                'humidity_min': 50, 'humidity_max': 70, 'humidity_optimal': 60,
                'season': ['ilkbahar', 'yaz'], 'type': 'sebze',
                'growth_days': 75, 'water_need': 'orta'
            },
            'patlican': {
                'temp_min': 22, 'temp_max': 35, 'temp_optimal': 28,
                'humidity_min': 60, 'humidity_max': 80, 'humidity_optimal': 70,
                'season': ['ilkbahar', 'yaz'], 'type': 'sebze',
                'growth_days': 85, 'water_need': 'yuksek'
            },
            'salatalik': {
                'temp_min': 18, 'temp_max': 28, 'temp_optimal': 23,
                'humidity_min': 70, 'humidity_max': 90, 'humidity_optimal': 80,
                'season': ['ilkbahar', 'yaz'], 'type': 'sebze',
                'growth_days': 60, 'water_need': 'yuksek'
            },
            'marul': {
                'temp_min': 10, 'temp_max': 25, 'temp_optimal': 18,
                'humidity_min': 60, 'humidity_max': 80, 'humidity_optimal': 70,
                'season': ['sonbahar', 'kis', 'ilkbahar'], 'type': 'sebze',
                'growth_days': 45, 'water_need': 'orta'
            },
            'ispanak': {
                'temp_min': 5, 'temp_max': 20, 'temp_optimal': 15,
                'humidity_min': 60, 'humidity_max': 80, 'humidity_optimal': 70,
                'season': ['sonbahar', 'kis', 'ilkbahar'], 'type': 'sebze',
                'growth_days': 40, 'water_need': 'orta'
            },
            # Ev Bitkileri
            'pothos': {
                'temp_min': 18, 'temp_max': 28, 'temp_optimal': 23,
                'humidity_min': 40, 'humidity_max': 60, 'humidity_optimal': 50,
                'season': ['tum_yil'], 'type': 'ev_bitkisi',
                'growth_days': 365, 'water_need': 'dusuk'
            },
            'monstera': {
                'temp_min': 20, 'temp_max': 30, 'temp_optimal': 25,
                'humidity_min': 50, 'humidity_max': 70, 'humidity_optimal': 60,
                'season': ['tum_yil'], 'type': 'ev_bitkisi',
                'growth_days': 365, 'water_need': 'orta'
            },
            'sansevieria': {
                'temp_min': 15, 'temp_max': 35, 'temp_optimal': 25,
                'humidity_min': 30, 'humidity_max': 50, 'humidity_optimal': 40,
                'season': ['tum_yil'], 'type': 'ev_bitkisi',
                'growth_days': 365, 'water_need': 'dusuk'
            },
            'ficus': {
                'temp_min': 18, 'temp_max': 26, 'temp_optimal': 22,
                'humidity_min': 40, 'humidity_max': 60, 'humidity_optimal': 50,
                'season': ['tum_yil'], 'type': 'ev_bitkisi',
                'growth_days': 365, 'water_need': 'orta'
            }
        }
        return plants
    
    def get_weather_data(self, lat=37.0662, lon=37.3833):
        """Gaziantep için hava durumu verilerini al (OpenWeatherMap API)"""
        import os
        from dotenv import load_dotenv

        load_dotenv()
        api_key = os.getenv('OPENWEATHER_API_KEY', 'YOUR_API_KEY')
        url = f"http://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={api_key}&units=metric"
        
        try:
            response = requests.get(url)
            data = response.json()
            
            weather_info = {
                'temperature': data['main']['temp'],
                'humidity': data['main']['humidity'],
                'pressure': data['main']['pressure'],
                'wind_speed': data['wind']['speed'],
                'description': data['weather'][0]['description']
            }
            return weather_info
        except:
            # Test verisi (API olmadığında)
            return {
                'temperature': 25.0,
                'humidity': 65.0,
                'pressure': 1013.0,
                'wind_speed': 3.5,
                'description': 'clear sky'
            }
    
    def get_season(self, date=None):
        """Mevsimi belirle"""
        if date is None:
            date = datetime.now()
        
        month = date.month
        if month in [12, 1, 2]:
            return 'kis'
        elif month in [3, 4, 5]:
            return 'ilkbahar'
        elif month in [6, 7, 8]:
            return 'yaz'
        else:
            return 'sonbahar'
    
    def create_features(self, temperature, humidity, plant_name, date=None):
        """Özellik vektörü oluştur"""
        if date is None:
            date = datetime.now()
        
        season = self.get_season(date)
        plant_info = self.plant_database.get(plant_name, {})
        
        features = {
            'temperature': temperature,
            'humidity': humidity,
            'month': date.month,
            'day_of_year': date.timetuple().tm_yday,
            'temp_optimal': plant_info.get('temp_optimal', 20),
            'humidity_optimal': plant_info.get('humidity_optimal', 60),
            'temp_diff': abs(temperature - plant_info.get('temp_optimal', 20)),
            'humidity_diff': abs(humidity - plant_info.get('humidity_optimal', 60)),
            'season_spring': 1 if season == 'ilkbahar' else 0,
            'season_summer': 1 if season == 'yaz' else 0,
            'season_autumn': 1 if season == 'sonbahar' else 0,
            'season_winter': 1 if season == 'kis' else 0,
            'is_vegetable': 1 if plant_info.get('type') == 'sebze' else 0,
            'water_need_low': 1 if plant_info.get('water_need') == 'dusuk' else 0,
            'water_need_medium': 1 if plant_info.get('water_need') == 'orta' else 0,
            'water_need_high': 1 if plant_info.get('water_need') == 'yuksek' else 0
        }
        
        return list(features.values())
    
    def calculate_suitability_score(self, temperature, humidity, plant_name, date=None):
        """Bitki için uygunluk skorunu hesapla (0-100)"""
        plant_info = self.plant_database.get(plant_name)
        if not plant_info:
            return 0
        
        season = self.get_season(date)
        
        # Sıcaklık skoru
        temp_min, temp_max = plant_info['temp_min'], plant_info['temp_max']
        temp_optimal = plant_info['temp_optimal']
        
        if temp_min <= temperature <= temp_max:
            temp_score = 100 - abs(temperature - temp_optimal) * 2
        else:
            temp_score = max(0, 100 - abs(temperature - temp_optimal) * 5)
        
        # Nem skoru
        hum_min, hum_max = plant_info['humidity_min'], plant_info['humidity_max']
        hum_optimal = plant_info['humidity_optimal']
        
        if hum_min <= humidity <= hum_max:
            humidity_score = 100 - abs(humidity - hum_optimal) * 1.5
        else:
            humidity_score = max(0, 100 - abs(humidity - hum_optimal) * 3)
        
        # Mevsim skoru
        suitable_seasons = plant_info['season']
        if 'tum_yil' in suitable_seasons or season in suitable_seasons:
            season_score = 100
        else:
            season_score = 30
        
        # Genel skor (ağırlıklı ortalama)
        total_score = (temp_score * 0.4 + humidity_score * 0.4 + season_score * 0.2)
        
        return max(0, min(100, total_score))
    
    def get_recommendation(self, temperature, humidity, plant_name, date=None):
        """Detaylı öneri ver"""
        score = self.calculate_suitability_score(temperature, humidity, plant_name, date)
        plant_info = self.plant_database.get(plant_name, {})
        
        if score >= 80:
            status = "Mükemmel"
            color = "green"
            advice = f"{plant_name.title()} için şartlar mükemmel! Ekime başlayabilirsiniz."
        elif score >= 60:
            status = "İyi"
            color = "lightgreen"
            advice = f"{plant_name.title()} için şartlar iyi. Dikkatli bakımla başarılı olabilirsiniz."
        elif score >= 40:
            status = "Orta"
            color = "orange"
            advice = f"{plant_name.title()} için şartlar orta. Ek önlemler almanız önerilir."
        else:
            status = "Zor"
            color = "red"
            advice = f"{plant_name.title()} için şartlar zor. Farklı bir zaman veya bitki seçmeyi düşünün."
        
        # Spesifik öneriler
        suggestions = []
        if temperature < plant_info.get('temp_min', 0):
            suggestions.append("Sıcaklık çok düşük. Sera veya kapalı alan kullanın.")
        elif temperature > plant_info.get('temp_max', 50):
            suggestions.append("Sıcaklık çok yüksek. Gölgeleme yapın.")
        
        if humidity < plant_info.get('humidity_min', 0):
            suggestions.append("Nem çok düşük. Sulama sıklığını artırın.")
        elif humidity > plant_info.get('humidity_max', 100):
            suggestions.append("Nem çok yüksek. Havalandırmayı artırın.")
        
        return {
            'plant_name': plant_name,
            'score': round(score, 1),
            'status': status,
            'color': color,
            'advice': advice,
            'suggestions': suggestions,
            'optimal_temp': plant_info.get('temp_optimal'),
            'optimal_humidity': plant_info.get('humidity_optimal'),
            'current_temp': temperature,
            'current_humidity': humidity
        }
    
    def get_all_recommendations(self, temperature, humidity, date=None):
        """Tüm bitkiler için önerileri al"""
        recommendations = []
        
        for plant_name in self.plant_database.keys():
            rec = self.get_recommendation(temperature, humidity, plant_name, date)
            recommendations.append(rec)
        
        # Skora göre sırala
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return recommendations

# Model kullanım örneği
if __name__ == "__main__":
    # Model oluştur
    model = PlantPredictionModel()
    
    # Test verisi
    temperature = 25.0
    humidity = 65.0
    
    print("=== GAZİANTEP BİTKİ ÖNERİ SİSTEMİ ===")
    print(f"Sıcaklık: {temperature}°C")
    print(f"Nem: {humidity}%")
    print(f"Tarih: {datetime.now().strftime('%d.%m.%Y')}")
    print("\n" + "="*50)
    
    # Tüm önerileri al
    recommendations = model.get_all_recommendations(temperature, humidity)
    
    # En iyi 5 öneriyi göster
    print("\nEN İYİ 5 ÖNERİ:")
    for i, rec in enumerate(recommendations[:5], 1):
        print(f"\n{i}. {rec['plant_name'].upper()}")
        print(f"   Skor: {rec['score']}/100 ({rec['status']})")
        print(f"   Öneri: {rec['advice']}")
        if rec['suggestions']:
            print(f"   Öneriler: {', '.join(rec['suggestions'])}")
